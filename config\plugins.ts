export default () => ({
  ckeditor: {
    enabled: true,
    config: {
      editor: {
        toolbar: {
          items: [
            "heading",
            "|",
            "fontFamily",
            "fontSize",
            "fontColor",
            "fontBackgroundColor",
            "highLight",
            "|",
            "bold",
            "italic",
            "underline",
            "strikethrough",
            "subscript",
            "superscript",
            "removeFormat",
            "|",
            "bulletedList",
            "numberedList",
            "todoList",
            "|",
            "indent",
            "outdent",
            "alignment",
            "|",
            "strapiMediaLib",
            "mediaEmbed",
            "insertImage",
            "|",
            "link",
            "|",
            "htmlEmbed",
            "code",
            "codeBlock",
            "|",
            "specialCharacters",
            "blockQuote",
            "insertTable",
            "horizontalLine",
            "|",
            "sourceEditing",
            "|",
            "fullScreen",
            "|",
            "undo",
            "redo",
          ],
          shouldNotGroupWhenFull: true,
        },
        fontSize: {
          options: [8, 10, 12, 14, 16, 18, 20, 22, 24, 26, 28, 30, 32, 34, 36],
          supportAllValues: false,
        },
        fontFamily: {
          options: [
            "default",
            "Poppins, sans-serif",
            "Raleway, sans-serif",
            "Helvetica, sans-serif",
            "Arial, Helvetica, sans-serif",
            "Courier New, Courier, monospace",
            "Georgia, serif",
            "Lucida Sans Unicode, Lucida Grande, sans-serif",
            "Tahoma, Geneva, sans-serif",
            "Times New Roman, Times, serif",
            "Trebuchet MS, Helvetica, sans-serif",
            "Verdana, Geneva, sans-serif",
            "JetBrains Mono, monospace",
            "Lato, Inter, sans-serif",
          ],
          supportAllValues: true,
        },
        fontColor: {
          colors: [
            { color: "#f05443", label: "MTL theme color" },
            { color: "hsl(0, 0%, 0%)", label: "Black" },
            { color: "hsl(0, 0%, 30%)", label: "Dim grey" },
            { color: "hsl(0, 0%, 60%)", label: "Grey" },
            { color: "hsl(0, 0%, 90%)", label: "Light grey" },
            { color: "hsl(0, 0%, 100%)", label: "White", hasBorder: true },
            { color: "hsl(0, 75%, 60%)", label: "Red" },
            { color: "hsl(30, 75%, 60%)", label: "Orange" },
            { color: "hsl(60, 75%, 60%)", label: "Yellow" },
            { color: "hsl(120, 75%, 60%)", label: "Green" },
            { color: "hsl(150, 75%, 60%)", label: "Aquamarine" },
            { color: "hsl(180, 75%, 60%)", label: "Turquoise" },
            { color: "hsl(210, 75%, 60%)", label: "Light blue" },
            { color: "hsl(240, 75%, 60%)", label: "Blue" },
            { color: "hsl(270, 75%, 60%)", label: "Purple" },
          ],
          columns: 5,
          documentColors: 10,
        },
        fontBackgroundColor: {
          colors: [
            { color: "#f05443", label: "MTL theme color" },
            { color: "hsl(0, 0%, 0%)", label: "Black" },
            { color: "hsl(0, 0%, 30%)", label: "Dim grey" },
            { color: "hsl(0, 0%, 60%)", label: "Grey" },
            { color: "hsl(0, 0%, 90%)", label: "Light grey" },
            { color: "hsl(0, 0%, 100%)", label: "White", hasBorder: true },
            { color: "hsl(0, 75%, 60%)", label: "Red" },
            { color: "hsl(30, 75%, 60%)", label: "Orange" },
            { color: "hsl(60, 75%, 60%)", label: "Yellow" },
            { color: "hsl(120, 75%, 60%)", label: "Green" },
            { color: "hsl(150, 75%, 60%)", label: "Aquamarine" },
            { color: "hsl(180, 75%, 60%)", label: "Turquoise" },
            { color: "hsl(210, 75%, 60%)", label: "Light blue" },
            { color: "hsl(240, 75%, 60%)", label: "Blue" },
            { color: "hsl(270, 75%, 60%)", label: "Purple" },
          ],
          columns: 5,
          documentColors: 10,
        },
        image: {
          resizeUnit: "%",
          resizeOptions: [
            {
              name: "resizeImage:original",
              value: null,
              icon: "original",
            },
            {
              name: "resizeImage:25",
              value: "25",
              icon: "small",
            },
            {
              name: "resizeImage:50",
              value: "50",
              icon: "medium",
            },
            {
              name: "resizeImage:75",
              value: "75",
              icon: "large",
            },
          ],
          toolbar: [
            "toggleImageCaption",
            "imageTextAlternative",
            "imageStyle:inline",
            "imageStyle:block",
            "imageStyle:side",
            "linkImage",
            "resizeImage:25",
            "resizeImage:50",
            "resizeImage:75",
            "resizeImage:original",
          ],
        },
        // https://ckeditor.com/docs/ckeditor5/latest/features/table.html
        table: {
          contentToolbar: [
            "tableColumn",
            "tableRow",
            "mergeTableCells",
            "tableCellProperties",
            "tableProperties",
            "toggleTableCaption",
          ],
        },
        // https://ckeditor.com/docs/ckeditor5/latest/features/headings.html
        heading: {
          options: [
            {
              model: "paragraph",
              title: "Paragraph",
              class: "ck-heading_paragraph",
            },
            {
              model: "heading1",
              view: "h1",
              title: "Heading 1",
              class: "ck-heading_heading1",
            },
            {
              model: "heading2",
              view: "h2",
              title: "Heading 2",
              class: "ck-heading_heading2",
            },
            {
              model: "heading3",
              view: "h3",
              title: "Heading 3",
              class: "ck-heading_heading3",
            },
            {
              model: "heading4",
              view: "h4",
              title: "Heading 4",
              class: "ck-heading_heading4",
            },
          ],
        },
        link: {
          defaultProtocol: "http://",
          decorators: [
            {
              mode: "manual",
              label: "Open in a new tab",
              defaultValue: true,
              attributes: {
                target: "_blank",
                rel: "noopener",
              },
            },
            {
              mode: "manual",
              label: "Downloadable",
              attributes: {
                download: "download",
                rel: "noopener",
              },
            },
            {
              mode: "manual",
              label: "no-follow",
              attributes: {
                target: "_blank",
                rel: "noopener noreferrer nofollow",
              },
            },
            {
              mode: "manual",
              label: "do-follow",
              attributes: {
                rel: "noopener",
              },
            },
          ],
        },
        htmlSupport: {
          allow: [
            {
              name: "img",
              attributes: {
                sizes: true,
                loading: true,
              },
            },
          ],
        },
      },
    },
  },
  // upload: {
  //   config: {
  //     provider: "@strapi-community/strapi-provider-upload-google-cloud-storage",
  //     providerOptions: {
  //       sizeLimit: 512 * 1024 * 1024,
  //       bucketName: "nextjs-cdn",
  //       cdnUrl: "https://cdn-gcp.new.marutitech.com",
  //       publicFiles: true,
  //       uniform: false,
  //       cacheMaxAge: ********,
  //       gzip: true,
  //       serviceAccount: {
  //         type: "service_account",
  //         project_id: "marutitech-website-production",
  //         private_key_id: "663f917b2a868b52e9bc8939fe87c8e1d108a2e1",
  //         private_key:
**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
  //         client_email:
  //           "<EMAIL>",
  //         client_id: "103225216557883733778",
  //         auth_uri: "https://accounts.google.com/o/oauth2/auth",
  //         token_uri: "https://oauth2.googleapis.com/token",
  //         auth_provider_x509_cert_url:
  //           "https://www.googleapis.com/oauth2/v1/certs",
  //         client_x509_cert_url:
  //           "https://www.googleapis.com/robot/v1/metadata/x509/storage-admin%40marutitech-website-production.iam.gserviceaccount.com",
  //       },
  //       baseUrl: "https://cdn-gcp.new.marutitech.com",
  //       metadata: (file : any) => ({
  //         cacheControl: `public, max-age=${60 * 60 * 24 * 30}`, // One week
  //       }),
  //       generateUploadFileName(file : any) {
  //         // console.log('Check' + file.hash);
  //         const extension = file.ext.toLowerCase();
  //         const fileName = file.hash;
  //         return `${fileName}${extension}`;
  //       },
  //     },
  //   },
  // },
  upload: {
    config: {
      provider: "@strapi-community/strapi-provider-upload-google-cloud-storage",
      providerOptions: {
        sizeLimit: 512 * 1024 * 1024,
        bucketName: "maruti-site-cdn",
        cdnUrl: "https://cdn.marutitech.com",
        publicFiles: true,
        uniform: false,
        cacheMaxAge: ********,
        gzip: true,
        serviceAccount: {
          type: "service_account",
          project_id: "marutitech-website-production",
          private_key_id: "663f917b2a868b52e9bc8939fe87c8e1d108a2e1",
          private_key:
***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
          client_email:
            "<EMAIL>",
          client_id: "103225216557883733778",
          auth_uri: "https://accounts.google.com/o/oauth2/auth",
          token_uri: "https://oauth2.googleapis.com/token",
          auth_provider_x509_cert_url:
            "https://www.googleapis.com/oauth2/v1/certs",
          client_x509_cert_url:
            "https://www.googleapis.com/robot/v1/metadata/x509/storage-admin%40marutitech-website-production.iam.gserviceaccount.com",
        },
        baseUrl: "https://cdn.marutitech.com",
        metadata: (file: any) => ({
          cacheControl: `public, max-age=${60 * 60 * 24 * 30}`, // One week
        }),
        generateUploadFileName(file: any) {
          // console.log('Check' + file.hash);
          const extension = file.ext.toLowerCase();
          const fileName = file.hash;
          return `${fileName}${extension}`;
        },
      },
    },
  },
  "import-export-entries": {
    enabled: true,
    resolve: "./src/plugins/import-export",
    config: {
      // See `Config` section.
    },
  },
  "publish-to-staging": {
    enabled: true,
    resolve: "./src/plugins/publish-to-staging",
  },
});
